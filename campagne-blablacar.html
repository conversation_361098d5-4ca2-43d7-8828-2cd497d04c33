<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BlaBlaCar - Campagne Nationale de Sensibilisation Écologique</title>
    <style>
        :root {
            --vert-sauge: #A7C4A0;
            --bleu-petrole: #3E6B7A;
            --gris-chaud: #F2F2F0;
            --beige-sable: #EADFC8;
            --bleu-blablacar: #00AFF5;
            --bleu-fonce: #003D82;
            --blanc: #FFFFFF;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--blanc);
            color: var(--bleu-fonce);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .blablacar-header {
            background: var(--blanc);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .blablacar-nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .blablacar-logo {
            display: flex;
            align-items: center;
            font-size: 1.5em;
            font-weight: bold;
            color: var(--bleu-blablacar);
        }

        .nav-links {
            display: flex;
            gap: 30px;
            align-items: center;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--bleu-fonce);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--bleu-blablacar);
        }

        .hero-banner {
            background: linear-gradient(135deg, var(--bleu-blablacar) 0%, var(--bleu-fonce) 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1.5" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="white" opacity="0.1"/></svg>') repeat;
            animation: float 20s linear infinite;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, var(--vert-sauge), var(--bleu-petrole));
            border-radius: 20px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="20">🚗</text></svg>') repeat;
            opacity: 0.1;
            animation: float 10s linear infinite;
        }

        h1 {
            font-size: 3em;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 2;
        }

        .subtitle {
            font-size: 1.3em;
            color: var(--bleu-petrole);
            font-weight: bold;
            background: rgba(255,255,255,0.95);
            padding: 15px 30px;
            border-radius: 50px;
            display: inline-block;
            position: relative;
            z-index: 2;
        }

        .campaign-badge {
            background: var(--beige-sable);
            color: var(--bleu-petrole);
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 0.9em;
            font-weight: bold;
            margin-bottom: 15px;
            display: inline-block;
            border: 2px solid var(--bleu-petrole);
        }

        .stats-section {
            background: var(--gris-chaud);
            padding: 60px 0;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .stat-card {
            background: white;
            padding: 40px 20px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: var(--bleu-blablacar);
            display: block;
            margin-bottom: 10px;
        }

        .stat-label {
            color: var(--bleu-fonce);
            font-weight: 500;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .impact-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 6px solid var(--vert-sauge);
            transition: transform 0.3s ease;
        }

        .impact-card:hover {
            transform: translateY(-5px);
        }

        .impact-number {
            font-size: 3em;
            font-weight: bold;
            color: var(--vert-sauge);
            display: block;
        }

        .comparison-section {
            padding: 80px 0;
            background: white;
        }

        .section-title {
            text-align: center;
            font-size: 2.5em;
            color: var(--bleu-fonce);
            margin-bottom: 20px;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.2em;
            color: var(--bleu-petrole);
            margin-bottom: 60px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin: 40px 0;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
            padding: 0 20px;
        }

        .constraint-card {
            background: #FFF5F5;
            border-radius: 20px;
            padding: 30px;
            border: 2px solid #FFE5E5;
            position: relative;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            min-height: 400px;
        }

        .constraint-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .constraint-card::before {
            content: '😤';
            position: absolute;
            top: -15px;
            right: -15px;
            font-size: 2em;
            background: #FF6B6B;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(255,107,107,0.3);
        }

        .easy-card {
            background: linear-gradient(135deg, var(--bleu-blablacar), var(--bleu-fonce));
            color: white;
            border-radius: 20px;
            padding: 30px;
            position: relative;
            transform: scale(1.02);
            box-shadow: 0 15px 40px rgba(0,175,245,0.3);
        }

        .easy-card::before {
            content: '😎';
            position: absolute;
            top: -15px;
            right: -15px;
            font-size: 2em;
            background: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .equation {
            background: var(--gris-chaud);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin: 40px 0;
            border: 3px dashed var(--vert-sauge);
        }

        .equation-text {
            font-size: 1.5em;
            font-weight: bold;
            color: var(--bleu-petrole);
            font-family: 'Courier New', monospace;
        }

        .cta-section {
            background: linear-gradient(135deg, var(--bleu-blablacar), var(--bleu-fonce));
            color: white;
            padding: 80px 0;
            text-align: center;
        }

        .cta-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .cta-button {
            background: white;
            color: var(--bleu-blablacar);
            padding: 20px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.3em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 20px 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .cta-button.secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .cta-button.secondary:hover {
            background: white;
            color: var(--bleu-blablacar);
        }

        .footer-section {
            background: var(--bleu-fonce);
            color: white;
            padding: 60px 0 40px;
            text-align: center;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: white;
            text-decoration: none;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .footer-links a:hover {
            opacity: 1;
        }

        .emoji {
            font-size: 1.5em;
            margin-right: 10px;
        }

        .highlight {
            background: var(--vert-sauge);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            h1 { font-size: 2em; }
            .hero-section { padding: 30px 20px; }
            .comparison-grid { grid-template-columns: 1fr; }
            .easy-card { transform: none; }
        }
    </style>
</head>
<body>
    <!-- Navigation BlaBlaCar -->
    <div class="blablacar-header">
        <div class="blablacar-nav">
            <div class="blablacar-logo">
                🚗 BlaBlaCar
            </div>
            <div class="nav-links">
                <a href="#">Covoiturage</a>
                <a href="#">Bus</a>
                <a href="#">BlaBlaCar Daily</a>
                <a href="#">🌱 Écologie</a>
            </div>
        </div>
    </div>

    <!-- Hero Banner -->
    <div class="hero-banner">
        <div class="container">
            <div style="position: relative; z-index: 2;">
                <div style="background: rgba(255,255,255,0.1); padding: 10px 20px; border-radius: 25px; display: inline-block; margin-bottom: 20px; font-size: 0.9em; font-weight: bold;">
                    🎯 CAMPAGNE NATIONALE DE SENSIBILISATION
                </div>
                <h1 style="font-size: 3.5em; margin-bottom: 20px; font-weight: 300;">
                    Un petit geste pour la planète c’est bien ! <br>
                    <strong>Mais covoiturer c’est mieux !</strong>
                </h1>
                <p style="font-size: 1.3em; margin-bottom: 30px; opacity: 0.9;">
                    La mobilité partagée, c'est l'avenir
                </p>
            </div>
        </div>
    </div>

    <div class="container">

        <!-- Section Statistiques -->
        <div class="stats-section">
            <div class="container">
                <h2 style="font-size: 2.5em; margin-bottom: 20px; color: var(--bleu-fonce);">
                    🌍 Ensemble, réduisons notre empreinte carbone
                </h2>
                <p style="font-size: 1.2em; margin-bottom: 50px; color: var(--bleu-petrole);">
                    "Pourquoi se torturer avec des gestes compliqués quand on peut sauver la planète... assis confortablement ?"
                </p>

                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number">6 kg</span>
                        <div class="stat-label">CO₂ économisés<br>par trajet</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">290 kg</span>
                        <div class="stat-label">CO₂ économisés par personne<br> par an</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">2M </span>
                        <div class="stat-label">Tonnes CO₂<br>évitées/an
                            <br> par nos utilisateurs
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Comparaison -->
        <div class="comparison-section">
            <div class="container">
                <h2 class="section-title">🤔 Tous les gestes écolos ne se valent pas</h2>
                <p class="section-subtitle">
                    Pendant que certains se compliquent la vie avec des gestes contraignants,
                    BlaBlaCar vous propose la solution la plus simple ET la plus efficace !
                </p>

                <div class="comparison-grid">
            <div class="constraint-card">
                <h3>🥩 ALIMENTATION : Le régime de la souffrance</h3>
                <p><strong>Remplacer bœuf → tofu :</strong> 35 kg CO₂/kg évités</p>
                <p><strong>La réalité :</strong></p>
                <ul>
                    <li>😭 Dire adieu au steak-frites du dimanche</li>
                    <li>🤢 Découvrir le "goût" du tofu nature</li>
                    <li>😵 Expliquer à mamie pourquoi tu refuses son rôti</li>
                    <li>💸 Payer plus cher pour des "alternatives" bizarres</li>
                    <li>🍽️ Passer 2h à cuisiner pour que ça ait du goût</li>
                </ul>
                <p class="highlight">Sacrifice quotidien garanti ! 😤</p>
            </div>

<!--            <div class="constraint-card">-->
<!--                <h3>👕 TEXTILE : La résistance aux tendances</h3>-->
<!--                <p><strong>Fast fashion :</strong> 270 kg CO₂/personne/an</p>-->
<!--                <p><strong>Contraintes :</strong></p>-->
<!--                <ul>-->
<!--                    <li>Résister aux soldes et nouveautés</li>-->
<!--                    <li>Porter les mêmes vêtements plus longtemps</li>-->
<!--                    <li>Ignorer les influenceurs mode</li>-->
<!--                    <li>Accepter de ne pas être "tendance"</li>-->
<!--                </ul>-->
<!--                <p class="highlight">Frustration garantie 😤</p>-->
<!--            </div>-->

            <div class="constraint-card">
                <h3>🧴 COSMÉTIQUES DIY : Le laboratoire de l'enfer</h3>
                <p><strong>Shampooing/dentifrice maison</strong></p>
                <p><strong>La vérité qui dérange :</strong></p>
                <ul>
                    <li>🧪 Transformer sa salle de bain en laboratoire</li>
                    <li>⏰ Passer son dimanche à mélanger du bicarbonate</li>
                    <li>🤮 Sentir le vinaigre de cidre toute la journée</li>
                    <li>😱 Avoir les cheveux qui ressemblent à de la paille</li>
                    <li>🦷 Se brosser les dents avec du sable (littéralement)</li>
                    <li>💀 Empoisonner sa famille avec tes "créations"</li>
                </ul>
                <p class="highlight">Économies : 50g de CO₂... Bravo champion ! 😤</p>
            </div>

            <div class="easy-card">
                <h3>🚗 COVOITURAGE : Le geste malin</h3>
                <p><strong>6 kg CO₂ économisés par trajet</strong></p>
                <p><strong>Avantages :</strong></p>
                <ul>
                    <li>✅ Confort garanti (tu restes assis !)</li>
                    <li>✅ Économies financières en bonus</li>
                    <li>✅ Rencontres et discussions sympa</li>
                    <li>✅ Impact immédiat et mesurable</li>
                    <li>✅ Zéro changement d'habitudes</li>
                </ul>
                <p class="highlight">L'écologie intelligente ! 😎</p>
            </div>
        </div>

<!--        <div class="equation">-->
<!--            <h2>🧮 L'ÉQUATION GAGNANTE</h2>-->
<!--            <div class="equation-text">-->
<!--                1 COVOITURAGE = 6 kg CO₂ économisés<br>-->
<!--                = 1 repas avec bœuf évité<br>-->
<!--                = 1 chemise polyester non achetée<br>-->
<!--                = Des dizaines de produits DIY-->
<!--            </div>-->
<!--        </div>-->

        </div>
        </div>

        <!-- Call to Action -->
        <div class="cta-section">
            <div class="cta-content">
                <h2 style="font-size: 2.8em; margin-bottom: 20px;">
                    🌱 Sauve la planète sans te prendre la tête
                </h2>
                <p style="font-size: 1.3em; margin-bottom: 40px; opacity: 0.9;">
                    Rejoins les millions de membres BlaBlaCar qui font la différence,
                    un trajet à la fois !
                </p>
                <div>
                    <a href="#" class="cta-button">
                        🚗 Je commence le covoiturage
                    </a>
                    <a href="#" class="cta-button secondary">
                        📱 Télécharger l'app
                    </a>
                </div>
                <p style="margin-top: 40px; font-style: italic; font-size: 1.1em;">
                    "La mobilité partagée, c'est l'avenir"
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer-section">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="#">À propos</a>
                    <a href="#">Aide</a>
                    <a href="#">Sécurité</a>
                    <a href="#">Conditions</a>
                    <a href="#">Confidentialité</a>
                    <a href="#">Écologie</a>
                </div>
                <p style="opacity: 0.7; font-size: 0.9em;">
                    © 2024 BlaBlaCar - Ensemble, réduisons notre empreinte carbone
                </p>
            </div>
        </div>
    </div>
</body>
</html>
